<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> <PERSON> VT Academy</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .fees-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #ecf0f1;
        }

        .page-header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .fee-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .summary-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.8rem;
            font-weight: 700;
        }

        .summary-card p {
            margin: 0;
            color: #7f8c8d;
            font-weight: 500;
        }

        .summary-card:nth-child(1) h3 { color: #3498db; }
        .summary-card:nth-child(2) h3 { color: #27ae60; }
        .summary-card:nth-child(3) h3 { color: #e74c3c; }
        .summary-card:nth-child(4) h3 { color: #f39c12; }

        .filter-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .filter-select {
            padding: 0.75rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            min-width: 150px;
        }

        .fees-table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .fees-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            min-width: 800px;
        }

        .fees-table th,
        .fees-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
        }

        .fees-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .fees-table th:first-child,
        .fees-table td:first-child {
            text-align: left;
            position: sticky;
            left: 0;
            background: white;
            z-index: 5;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }

        .fees-table th:first-child {
            background: #f8f9fa;
            z-index: 15;
        }

        .student-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            min-width: 200px;
        }

        .student-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            object-fit: cover;
        }

        .student-details {
            text-align: left;
        }

        .student-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .student-class {
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        .fee-checkbox {
            width: 20px;
            height: 20px;
            accent-color: #27ae60;
            cursor: pointer;
        }

        .month-header {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            min-width: 80px;
        }

        .paid-cell {
            background: #d4edda !important;
        }

        .unpaid-cell {
            background: #f8d7da !important;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }

        .no-students {
            text-align: center;
            padding: 3rem;
            color: #7f8c8d;
        }

        .no-students i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #bdc3c7;
        }

        @media (max-width: 768px) {
            .fee-summary {
                grid-template-columns: 1fr 1fr;
            }
            
            .filter-controls {
                flex-direction: column;
            }
            
            .fees-table th,
            .fees-table td {
                padding: 0.5rem;
                font-size: 0.9rem;
            }
            
            .student-info {
                min-width: 150px;
            }
            
            .month-header {
                min-width: 60px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../index.html">Trang Chủ</a></li>
                    <li><a href="../../classes/">Lớp Học</a></li>
                    <li><a href="../../achievements/">Thành Tích</a></li>
                    <li><a href="../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../research/">Nghiên Cứu</a></li>
                    <li><a href="../../auth/">Tài Khoản</a></li>
                    <li><a href="../" class="active">Quản Trị</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <div class="fees-container">
                <div class="page-header">
                    <h1><i class="fas fa-credit-card"></i> Quản Lý Học Phí</h1>
                    <a href="../" class="back-btn">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>

                <!-- Fee Summary -->
                <div class="fee-summary">
                    <div class="summary-card">
                        <h3 id="totalStudents">0</h3>
                        <p>Tổng học viên</p>
                    </div>
                    <div class="summary-card">
                        <h3 id="paidThisMonth">0</h3>
                        <p>Đã đóng tháng này</p>
                    </div>
                    <div class="summary-card">
                        <h3 id="unpaidThisMonth">0</h3>
                        <p>Chưa đóng tháng này</p>
                    </div>
                    <div class="summary-card">
                        <h3 id="monthlyRevenue">0đ</h3>
                        <p>Doanh thu tháng</p>
                    </div>
                </div>

                <!-- Filter Controls -->
                <div class="filter-controls">
                    <select id="classFilter" class="filter-select">
                        <option value="">Tất cả lớp học</option>
                        <option value="python-a">Python & AI - Lớp A</option>
                        <option value="python-b">Python & AI - Lớp B</option>
                        <option value="python-c">Python & AI - Lớp C</option>
                    </select>
                    <select id="monthFilter" class="filter-select">
                        <option value="">Tất cả tháng</option>
                        <!-- Months will be populated by JavaScript -->
                    </select>
                    <select id="statusFilter" class="filter-select">
                        <option value="">Tất cả trạng thái</option>
                        <option value="paid">Đã đóng</option>
                        <option value="unpaid">Chưa đóng</option>
                    </select>
                </div>

                <!-- Fees Table -->
                <div class="fees-table-container">
                    <div id="feesTableContainer">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Đang tải dữ liệu học phí...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-brand">
                    <div class="footer-logo">
                        <img src="../../assets/images/logo.jpg" alt="Vthon Logo">
                        <span>Vthon Academy</span>
                    </div>
                    <p>Học, học nữa, học mãi</p>
                    <p class="footer-copyright">© 2025 – All rights reserved</p>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { 
            getFirestore, 
            doc, 
            getDoc, 
            setDoc, 
            updateDoc, 
            collection, 
            query, 
            getDocs, 
            where
        } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        let studentsData = [];
        let feePayments = [];
        let months = [];

        // Check admin access
        onAuthStateChanged(auth, async (user) => {
            if (!user || user.email !== '<EMAIL>') {
                window.location.href = '../../auth/';
                return;
            }

            await initializeFeeManagement();
        });

        // Initialize fee management
        async function initializeFeeManagement() {
            try {
                // Generate months from June 2025 onwards
                generateMonths();
                
                // Load students and fee data
                await loadStudentsData();
                await loadFeePayments();
                
                // Initialize fee payments for all students
                await initializeFeePayments();
                
                // Display data
                displayFeeTable();
                updateSummary();
                setupEventListeners();
                
            } catch (error) {
                console.error('Error initializing fee management:', error);
                showError('Lỗi khi khởi tạo quản lý học phí');
            }
        }

        // Generate months from June 2025
        function generateMonths() {
            const startDate = new Date(2025, 5, 1); // June 2025 (month is 0-indexed)
            const currentDate = new Date();
            const endDate = new Date(currentDate.getFullYear() + 1, currentDate.getMonth(), 1);
            
            months = [];
            const monthFilter = document.getElementById('monthFilter');
            
            for (let date = new Date(startDate); date <= endDate; date.setMonth(date.getMonth() + 1)) {
                const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
                const monthName = date.toLocaleDateString('vi-VN', { year: 'numeric', month: 'long' });
                
                months.push({ key: monthKey, name: monthName });
                
                // Add to filter dropdown
                const option = document.createElement('option');
                option.value = monthKey;
                option.textContent = monthName;
                monthFilter.appendChild(option);
            }
        }

        // Load students data
        async function loadStudentsData() {
            const usersQuery = query(collection(db, "users"));
            const usersSnapshot = await getDocs(usersQuery);
            
            studentsData = [];
            usersSnapshot.forEach((doc) => {
                const userData = doc.data();
                if (userData.email !== '<EMAIL>' && userData.fullName) {
                    studentsData.push({
                        id: doc.id,
                        ...userData
                    });
                }
            });
        }

        // Load fee payments
        async function loadFeePayments() {
            const paymentsQuery = query(collection(db, "feePayments"));
            const paymentsSnapshot = await getDocs(paymentsQuery);
            
            feePayments = [];
            paymentsSnapshot.forEach((doc) => {
                feePayments.push({
                    id: doc.id,
                    ...doc.data()
                });
            });
        }

        // Initialize fee payments for all students and months
        async function initializeFeePayments() {
            for (const student of studentsData) {
                for (const month of months) {
                    const paymentId = `${student.id}_${month.key}`;
                    const existingPayment = feePayments.find(p => p.id === paymentId);
                    
                    if (!existingPayment) {
                        const paymentData = {
                            studentId: student.id,
                            studentName: student.fullName,
                            classId: student.courseClass || 'no-class',
                            month: month.key,
                            isPaid: false,
                            amount: 250000,
                            createdAt: new Date().toISOString()
                        };
                        
                        await setDoc(doc(db, "feePayments", paymentId), paymentData);
                        feePayments.push({ id: paymentId, ...paymentData });
                    }
                }
            }
        }

        // Display fee table
        function displayFeeTable() {
            const container = document.getElementById('feesTableContainer');
            
            if (studentsData.length === 0) {
                container.innerHTML = `
                    <div class="no-students">
                        <i class="fas fa-users"></i>
                        <h3>Chưa có học viên nào</h3>
                        <p>Dữ liệu học phí sẽ hiển thị khi có học viên đăng ký</p>
                    </div>
                `;
                return;
            }

            let tableHTML = `
                <table class="fees-table">
                    <thead>
                        <tr>
                            <th>Học viên</th>
            `;

            // Add month headers
            months.forEach(month => {
                tableHTML += `<th class="month-header">${month.name}</th>`;
            });

            tableHTML += `
                        </tr>
                    </thead>
                    <tbody>
            `;

            // Add student rows
            studentsData.forEach(student => {
                const studentClasses = getStudentClasses(student);
                const classNames = studentClasses.map(cls => getClassName(cls)).join(', ') || 'Chưa chọn lớp';

                tableHTML += `
                    <tr>
                        <td>
                            <div class="student-info">
                                <img src="${student.avatar ? student.avatar.replace('../assets/', '../../assets/') : '../../assets/images/avatars/avatar_boy_1.png'}"
                                     alt="Avatar" class="student-avatar">
                                <div class="student-details">
                                    <div class="student-name">${student.fullName}</div>
                                    <div class="student-class">${classNames}</div>
                                </div>
                            </div>
                        </td>
                `;

                // Add payment checkboxes for each month
                months.forEach(month => {
                    const paymentId = `${student.id}_${month.key}`;
                    const payment = feePayments.find(p => p.id === paymentId);
                    const isPaid = payment ? payment.isPaid : false;
                    const cellClass = isPaid ? 'paid-cell' : 'unpaid-cell';

                    tableHTML += `
                        <td class="${cellClass}">
                            <input type="checkbox" class="fee-checkbox" 
                                   data-student-id="${student.id}" 
                                   data-month="${month.key}"
                                   ${isPaid ? 'checked' : ''}
                                   onchange="updatePaymentStatus('${student.id}', '${month.key}', this.checked)">
                        </td>
                    `;
                });

                tableHTML += `</tr>`;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            container.innerHTML = tableHTML;
        }

        // Update payment status
        window.updatePaymentStatus = async function(studentId, month, isPaid) {
            try {
                const paymentId = `${studentId}_${month}`;
                const student = studentsData.find(s => s.id === studentId);
                
                const paymentData = {
                    studentId: studentId,
                    studentName: student.fullName,
                    classId: student.courseClass || 'no-class',
                    month: month,
                    isPaid: isPaid,
                    amount: 250000,
                    updatedAt: new Date().toISOString()
                };

                if (isPaid) {
                    paymentData.paidDate = new Date().toISOString();
                }

                await setDoc(doc(db, "feePayments", paymentId), paymentData);
                
                // Update local data
                const existingPaymentIndex = feePayments.findIndex(p => p.id === paymentId);
                if (existingPaymentIndex !== -1) {
                    feePayments[existingPaymentIndex] = { id: paymentId, ...paymentData };
                } else {
                    feePayments.push({ id: paymentId, ...paymentData });
                }

                // Update cell appearance
                const checkbox = document.querySelector(`input[data-student-id="${studentId}"][data-month="${month}"]`);
                const cell = checkbox.parentElement;
                cell.className = isPaid ? 'paid-cell' : 'unpaid-cell';

                // Update summary
                updateSummary();

                const statusText = isPaid ? 'đã đóng' : 'chưa đóng';
                const monthName = months.find(m => m.key === month)?.name || month;
                showSuccess(`Cập nhật trạng thái ${statusText} cho ${student.fullName} - ${monthName}`);

            } catch (error) {
                console.error('Error updating payment status:', error);
                showError('Lỗi khi cập nhật trạng thái thanh toán');
            }
        };

        // Update summary statistics
        function updateSummary() {
            // Get selected month from filter, default to current month
            const selectedMonth = document.getElementById('monthFilter').value || new Date().toISOString().slice(0, 7);
            const selectedMonthPayments = feePayments.filter(p => p.month === selectedMonth);

            const totalStudents = studentsData.length;
            const paidThisMonth = selectedMonthPayments.filter(p => p.isPaid).length;
            const unpaidThisMonth = totalStudents - paidThisMonth;
            const monthlyRevenue = paidThisMonth * 250000;

            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('paidThisMonth').textContent = paidThisMonth;
            document.getElementById('unpaidThisMonth').textContent = unpaidThisMonth;
            document.getElementById('monthlyRevenue').textContent = formatCurrency(monthlyRevenue);
        }

        // Get student classes
        function getStudentClasses(student) {
            const classes = [];
            
            if (student.allowedClasses && Array.isArray(student.allowedClasses)) {
                classes.push(...student.allowedClasses);
            }
            
            if (classes.length === 0 && student.courseClass) {
                classes.push(student.courseClass);
            }
            
            return [...new Set(classes)];
        }

        // Get class display name
        function getClassName(classId) {
            const classNames = {
                'python-a': 'Lớp A',
                'python-b': 'Lớp B', 
                'python-c': 'Lớp C'
            };
            return classNames[classId] || classId;
        }

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('classFilter').addEventListener('change', applyFilters);
            document.getElementById('monthFilter').addEventListener('change', applyFilters);
            document.getElementById('statusFilter').addEventListener('change', applyFilters);
        }

        // Apply filters (placeholder for future implementation)
        function applyFilters() {
            // This would filter the table based on selected criteria
            // For now, just refresh the display
            displayFeeTable();
            // Update summary for selected month
            updateSummary();
        }

        // Utility functions
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            
            const container = document.querySelector('.container');
            container.insertBefore(errorDiv, container.firstChild);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success-message';
            successDiv.textContent = message;
            
            const container = document.querySelector('.container');
            container.insertBefore(successDiv, container.firstChild);
            
            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
