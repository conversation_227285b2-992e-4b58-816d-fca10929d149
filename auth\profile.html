<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Vthon Academy</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Profile Page Styles */
        .profile-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .profile-header {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .profile-avatar {
            position: relative;
            width: 120px;
            height: 120px;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #667eea;
        }

        .profile-avatar .edit-overlay {
            position: absolute;
            bottom: 0;
            right: 0;
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .profile-avatar .edit-overlay:hover {
            background: #5a67d8;
            transform: scale(1.1);
        }

        .profile-info h1 {
            margin: 0 0 10px 0;
            color: #2d3748;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .profile-info .email {
            color: #667eea;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .profile-stats {
            display: flex;
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            min-width: 80px;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .profile-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .profile-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f7fafc;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .inbox-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .inbox-item:hover {
            background: #f7fafc;
            transform: translateX(5px);
        }

        .inbox-item.unread {
            background: #ebf8ff;
            border-left-color: #667eea;
        }

        .inbox-item.unread .message-title {
            font-weight: 600;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .message-content {
            flex: 1;
        }

        .message-title {
            font-size: 1rem;
            color: #2d3748;
            margin: 0 0 5px 0;
        }

        .message-preview {
            font-size: 0.9rem;
            color: #718096;
            margin: 0;
        }

        .message-time {
            font-size: 0.8rem;
            color: #a0aec0;
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .achievement-badge {
            text-align: center;
            padding: 15px;
            border-radius: 12px;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #744210;
            transition: all 0.3s ease;
        }

        .achievement-badge:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
        }

        .achievement-icon {
            font-size: 2rem;
            margin-bottom: 8px;
        }

        .achievement-name {
            font-size: 0.9rem;
            font-weight: 600;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #e53e3e;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .profile-header {
                flex-direction: column;
                text-align: center;
            }

            .profile-content {
                grid-template-columns: 1fr;
            }

            .profile-stats {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <ul class="nav-menu">
                <li><a href="../index.html">Trang Chủ</a></li>
                <li><a href="../classes/">Lớp Học</a></li>
                <li><a href="../achievements/">Thành Tích</a></li>
                <li><a href="register.html">Đăng Ký</a></li>
                <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                <li><a href="../research/">Nghiên Cứu</a></li>
                <li class="nav-account">
                    <a href="index.html" class="account-link">
                        <i class="fas fa-user"></i> Tài Khoản
                        <span id="accountNotificationBadge" class="notification-badge" style="display: none;">3</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Profile Container -->
    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-avatar">
                <img id="profileAvatarImg" src="../assets/images/avatars/avatar_boy_1.png" alt="Avatar">
                <div class="edit-overlay" onclick="openAvatarModal()">
                    <i class="fas fa-camera"></i>
                </div>
            </div>
            <div class="profile-info">
                <h1 id="profileName">Lê Quang Vinh</h1>
                <div class="email" id="profileEmail"><EMAIL></div>
                <div class="profile-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="statClasses">0</span>
                        <span class="stat-label">Lớp học</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="statAssignments">2</span>
                        <span class="stat-label">Bài tập</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="statRank">-</span>
                        <span class="stat-label">Xếp hạng</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="statAchievements">0</span>
                        <span class="stat-label">Huy hiệu</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Content -->
        <div class="profile-content">
            <!-- Inbox Card -->
            <div class="profile-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <h3 class="card-title">Hộp Thư</h3>
                    <div style="margin-left: auto; position: relative;">
                        <span class="notification-badge">3</span>
                    </div>
                </div>
                <div id="inboxContent">
                    <!-- Inbox items will be populated by JavaScript -->
                </div>
                <div style="text-align: center; margin-top: 15px;">
                    <a href="#" class="action-btn" onclick="openInboxModal()">
                        <i class="fas fa-envelope-open"></i> Xem tất cả
                    </a>
                </div>
            </div>

            <!-- Achievements Card -->
            <div class="profile-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="card-title">Huy Hiệu & Thành Tích</h3>
                </div>
                <div class="achievements-grid" id="achievementsGrid">
                    <!-- Achievements will be populated by JavaScript -->
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="profile-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="card-title">Thao Tác Nhanh</h3>
                </div>
                <div class="quick-actions">
                    <a href="../classes/" class="action-btn">
                        <i class="fas fa-chalkboard-teacher"></i> Lớp học của tôi
                    </a>
                    <a href="../rankings/" class="action-btn">
                        <i class="fas fa-chart-line"></i> Bảng xếp hạng
                    </a>
                    <a href="../achievements/" class="action-btn">
                        <i class="fas fa-star"></i> Thành tích
                    </a>
                    <a href="../admin/" class="action-btn" id="adminQuickAction" style="display: none;">
                        <i class="fas fa-cog"></i> Quản trị
                    </a>
                </div>
            </div>

            <!-- Personal Info Card -->
            <div class="profile-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-user-edit"></i>
                    </div>
                    <h3 class="card-title">Thông Tin Cá Nhân</h3>
                </div>
                <div id="personalInfoContent">
                    <!-- Personal info will be populated by JavaScript -->
                </div>
                <div style="text-align: center; margin-top: 15px;">
                    <a href="index.html" class="action-btn">
                        <i class="fas fa-edit"></i> Chỉnh sửa thông tin
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-section">
                <div class="footer-logo">
                    <img src="../assets/images/logo.jpg" alt="VTA Logo">
                    <span>Vthon Academy</span>
                </div>
                <p>Học, học nữa, học mãi.</p>
                <p>&copy; 2025 – All rights reserved.</p>
            </div>
            <div class="footer-section">
                <h3>Liên hệ</h3>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-phone"></i> 0399787678</p>
                <p><i class="fab fa-facebook"></i> Zalo: 0399787678</p>
            </div>
            <div class="footer-section">
                <h3>Mạng xã hội</h3>
                <div class="social-links">
                    <a href="https://www.facebook.com/vinhle030904/" target="_blank">
                        <i class="fab fa-facebook"></i> Facebook
                    </a>
                    <a href="https://www.tiktok.com/@sunnii39" target="_blank">
                        <i class="fab fa-tiktok"></i> TikTok
                    </a>
                    <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank">
                        <i class="fab fa-instagram"></i> Instagram
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase and JavaScript -->
    <script type="module">
        // Firebase imports
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, onAuthStateChanged, signOut } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, getDoc, setDoc, collection, query, where, orderBy, limit, getDocs } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Check authentication and load profile
        onAuthStateChanged(auth, async (user) => {
            if (user) {
                await loadUserProfile(user);
                await loadInboxMessages(user);
                await loadAchievements(user);
                updateNotificationBadge();
            } else {
                // Redirect to login if not authenticated
                window.location.href = 'index.html';
            }
        });

        // Load user profile data
        async function loadUserProfile(user) {
            try {
                const userDoc = await getDoc(doc(db, "users", user.uid));
                if (userDoc.exists()) {
                    const userData = userDoc.data();
                    
                    // Update profile info
                    document.getElementById('profileName').textContent = userData.fullName || user.displayName || 'Học viên';
                    document.getElementById('profileEmail').textContent = user.email;
                    
                    // Update avatar
                    if (userData.avatar) {
                        document.getElementById('profileAvatarImg').src = userData.avatar;
                    }
                    
                    // Update stats
                    document.getElementById('statClasses').textContent = userData.classCount || 0;
                    document.getElementById('statAssignments').textContent = userData.assignmentCount || 0;
                    document.getElementById('statRank').textContent = userData.rank || '-';
                    document.getElementById('statAchievements').textContent = userData.achievementCount || 0;
                    
                    // Show admin quick action if user is admin
                    if (user.email === '<EMAIL>') {
                        document.getElementById('adminQuickAction').style.display = 'flex';
                    }
                    
                    // Load personal info
                    loadPersonalInfo(userData);
                }
            } catch (error) {
                console.error("Error loading user profile:", error);
            }
        }

        // Load personal info
        function loadPersonalInfo(userData) {
            const personalInfoContent = document.getElementById('personalInfoContent');
            personalInfoContent.innerHTML = `
                <div style="display: grid; gap: 15px;">
                    <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #e2e8f0;">
                        <span style="font-weight: 500; color: #4a5568;">Họ và tên:</span>
                        <span style="color: #2d3748;">${userData.fullName || 'Chưa cập nhật'}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #e2e8f0;">
                        <span style="font-weight: 500; color: #4a5568;">Giới tính:</span>
                        <span style="color: #2d3748;">${userData.gender || 'Chưa cập nhật'}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #e2e8f0;">
                        <span style="font-weight: 500; color: #4a5568;">Ngày sinh:</span>
                        <span style="color: #2d3748;">${userData.birthdate || 'Chưa cập nhật'}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #e2e8f0;">
                        <span style="font-weight: 500; color: #4a5568;">Lớp quan tâm:</span>
                        <span style="color: #2d3748;">${userData.courseClass || 'Chưa chọn'}</span>
                    </div>
                </div>
            `;
        }

        // Load inbox messages (demo data for now)
        async function loadInboxMessages(user) {
            const inboxContent = document.getElementById('inboxContent');
            
            // Demo messages - in real app, this would come from Firestore
            const messages = [
                {
                    id: 1,
                    from: 'Giáo viên Vinh',
                    title: 'Thông báo bài tập mới',
                    preview: 'Bài tập Python cơ bản đã được cập nhật...',
                    time: '2 giờ trước',
                    unread: true
                },
                {
                    id: 2,
                    from: 'Hệ thống',
                    title: 'Chúc mừng hoàn thành bài tập',
                    preview: 'Bạn đã hoàn thành xuất sắc bài tập...',
                    time: '1 ngày trước',
                    unread: true
                },
                {
                    id: 3,
                    from: 'Admin',
                    title: 'Cập nhật thông tin lớp học',
                    preview: 'Lịch học tuần tới có thay đổi...',
                    time: '3 ngày trước',
                    unread: false
                }
            ];

            inboxContent.innerHTML = messages.map(msg => `
                <div class="inbox-item ${msg.unread ? 'unread' : ''}" onclick="openMessage(${msg.id})">
                    <div class="message-avatar">
                        ${msg.from.charAt(0)}
                    </div>
                    <div class="message-content">
                        <div class="message-title">${msg.title}</div>
                        <div class="message-preview">${msg.preview}</div>
                    </div>
                    <div class="message-time">${msg.time}</div>
                </div>
            `).join('');
        }

        // Load achievements (demo data)
        async function loadAchievements(user) {
            const achievementsGrid = document.getElementById('achievementsGrid');
            
            // Demo achievements
            const achievements = [
                { icon: '🏆', name: 'Học viên xuất sắc' },
                { icon: '🎯', name: 'Hoàn thành đúng hạn' },
                { icon: '💻', name: 'Python Master' },
                { icon: '⭐', name: 'Top 10' }
            ];

            achievementsGrid.innerHTML = achievements.map(achievement => `
                <div class="achievement-badge">
                    <div class="achievement-icon">${achievement.icon}</div>
                    <div class="achievement-name">${achievement.name}</div>
                </div>
            `).join('');
        }

        // Update notification badge
        function updateNotificationBadge() {
            const badge = document.getElementById('accountNotificationBadge');
            const unreadCount = 3; // This would come from actual unread messages count
            
            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }

        // Global functions
        window.openAvatarModal = function() {
            // Redirect to main account page for avatar selection
            window.location.href = 'index.html#avatar';
        };

        window.openInboxModal = function() {
            alert('Inbox modal sẽ được implement sau!');
        };

        window.openMessage = function(messageId) {
            alert(`Mở tin nhắn ID: ${messageId}`);
        };
    </script>
</body>
</html>
